import {
  pgTable,
  text,
  varchar,
  timestamp,
  jsonb,
  index,
  serial,
  integer,
  boolean,
  pgEnum
} from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Session storage table for Replit Auth
export const sessions = pgTable(
  "sessions",
  {
    sid: varchar("sid").primaryKey(),
    sess: jsonb("sess").notNull(),
    expire: timestamp("expire").notNull(),
  },
  (table) => [index("IDX_session_expire").on(table.expire)],
);

// User roles enum
export const userRoleEnum = pgEnum("user_role", ["entrepreneur", "eso_admin", "mentor", "sponsor"]);
export const milestoneStatusEnum = pgEnum("milestone_status", ["locked", "in_progress", "completed", "under_review"]);
export const teamMemberStatusEnum = pgEnum("team_member_status", ["active", "inactive", "pending"]);

// User storage table for Replit Auth
export const users = pgTable("users", {
  id: varchar("id").primaryKey().notNull(),
  email: varchar("email").unique(),
  firstName: varchar("first_name"),
  lastName: varchar("last_name"),
  profileImageUrl: varchar("profile_image_url"),
  role: userRoleEnum("role").notNull().default("entrepreneur"),
  location: varchar("location"),
  interests: text("interests").array(),
  teamPreference: varchar("team_preference"), // "solo" or "team"
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// ESO (Entrepreneur Support Organizations)
export const esos = pgTable("esos", {
  id: serial("id").primaryKey(),
  adminUserId: varchar("admin_user_id").notNull().references(() => users.id),
  organizationName: varchar("organization_name").notNull(),
  location: varchar("location").notNull(),
  description: text("description"),
  aspirationalCities: text("aspirational_cities").array(),
  brandName: varchar("brand_name"),
  colorScheme: varchar("color_scheme"),
  logoUrl: varchar("logo_url"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Startup ideas
export const startupIdeas = pgTable("startup_ideas", {
  id: serial("id").primaryKey(),
  title: varchar("title").notNull(),
  description: text("description").notNull(),
  category: varchar("category"),
  esoId: integer("eso_id").references(() => esos.id),
  createdById: varchar("created_by_id").references(() => users.id),
  isPremium: boolean("is_premium").default(false),
  createdAt: timestamp("created_at").defaultNow(),
});

// Teams
export const teams = pgTable("teams", {
  id: serial("id").primaryKey(),
  name: varchar("name").notNull(),
  startupIdeaId: integer("startup_idea_id").references(() => startupIdeas.id),
  leaderId: varchar("leader_id").notNull().references(() => users.id),
  description: text("description"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Team members
export const teamMembers = pgTable("team_members", {
  id: serial("id").primaryKey(),
  teamId: integer("team_id").notNull().references(() => teams.id),
  userId: varchar("user_id").notNull().references(() => users.id),
  role: varchar("role"), // "Product Lead", "Developer", "Marketing", etc.
  status: teamMemberStatusEnum("status").default("active"),
  joinedAt: timestamp("joined_at").defaultNow(),
});

// Milestones
export const milestones = pgTable("milestones", {
  id: serial("id").primaryKey(),
  startupIdeaId: integer("startup_idea_id").notNull().references(() => startupIdeas.id),
  title: varchar("title").notNull(),
  description: text("description").notNull(),
  order: integer("order").notNull(),
  requirements: text("requirements").array(),
  estimatedDays: integer("estimated_days"),
  createdAt: timestamp("created_at").defaultNow(),
});

// User milestone progress
export const userMilestoneProgress = pgTable("user_milestone_progress", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull().references(() => users.id),
  milestoneId: integer("milestone_id").notNull().references(() => milestones.id),
  teamId: integer("team_id").references(() => teams.id),
  status: milestoneStatusEnum("status").default("locked"),
  progressDescription: text("progress_description"),
  attachments: text("attachments").array(), // file URLs
  feedback: text("feedback"),
  reviewedBy: varchar("reviewed_by").references(() => users.id),
  startedAt: timestamp("started_at"),
  completedAt: timestamp("completed_at"),
  dueDate: timestamp("due_date"),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Mentor profiles
export const mentorProfiles = pgTable("mentor_profiles", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull().references(() => users.id),
  expertise: text("expertise").array(),
  industries: text("industries").array(),
  experience: text("experience"),
  availability: varchar("availability"),
  hourlyRate: integer("hourly_rate"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Sponsor profiles
export const sponsorProfiles = pgTable("sponsor_profiles", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull().references(() => users.id),
  companyName: varchar("company_name"),
  fundingCategories: text("funding_categories").array(),
  interests: text("interests").array(),
  minFunding: integer("min_funding"),
  maxFunding: integer("max_funding"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Mentor assignments
export const mentorAssignments = pgTable("mentor_assignments", {
  id: serial("id").primaryKey(),
  mentorId: varchar("mentor_id").notNull().references(() => users.id),
  entrepreneurId: varchar("entrepreneur_id").notNull().references(() => users.id),
  teamId: integer("team_id").references(() => teams.id),
  milestoneId: integer("milestone_id").references(() => milestones.id),
  status: varchar("status").default("active"), // "active", "completed", "declined"
  assignedAt: timestamp("assigned_at").defaultNow(),
});

// Relations
export const usersRelations = relations(users, ({ many, one }) => ({
  teamMemberships: many(teamMembers),
  ledTeams: many(teams),
  milestoneProgress: many(userMilestoneProgress),
  mentorProfile: one(mentorProfiles),
  sponsorProfile: one(sponsorProfiles),
  esoAdmin: one(esos),
}));

export const teamsRelations = relations(teams, ({ one, many }) => ({
  leader: one(users, { fields: [teams.leaderId], references: [users.id] }),
  members: many(teamMembers),
  startupIdea: one(startupIdeas, { fields: [teams.startupIdeaId], references: [startupIdeas.id] }),
}));

export const teamMembersRelations = relations(teamMembers, ({ one }) => ({
  team: one(teams, { fields: [teamMembers.teamId], references: [teams.id] }),
  user: one(users, { fields: [teamMembers.userId], references: [users.id] }),
}));

export const startupIdeasRelations = relations(startupIdeas, ({ many, one }) => ({
  teams: many(teams),
  milestones: many(milestones),
  eso: one(esos, { fields: [startupIdeas.esoId], references: [esos.id] }),
  createdBy: one(users, { fields: [startupIdeas.createdById], references: [users.id] }),
}));

export const milestonesRelations = relations(milestones, ({ one, many }) => ({
  startupIdea: one(startupIdeas, { fields: [milestones.startupIdeaId], references: [startupIdeas.id] }),
  userProgress: many(userMilestoneProgress),
}));

export const userMilestoneProgressRelations = relations(userMilestoneProgress, ({ one }) => ({
  user: one(users, { fields: [userMilestoneProgress.userId], references: [users.id] }),
  milestone: one(milestones, { fields: [userMilestoneProgress.milestoneId], references: [milestones.id] }),
  team: one(teams, { fields: [userMilestoneProgress.teamId], references: [teams.id] }),
  reviewer: one(users, { fields: [userMilestoneProgress.reviewedBy], references: [users.id] }),
}));

// Insert schemas
export const insertUserSchema = createInsertSchema(users).omit({
  createdAt: true,
  updatedAt: true,
});

export const insertEsoSchema = createInsertSchema(esos).omit({
  id: true,
  createdAt: true,
});

export const insertStartupIdeaSchema = createInsertSchema(startupIdeas).omit({
  id: true,
  createdAt: true,
});

export const insertTeamSchema = createInsertSchema(teams).omit({
  id: true,
  createdAt: true,
});

export const insertMilestoneProgressSchema = createInsertSchema(userMilestoneProgress).omit({
  id: true,
  updatedAt: true,
});

export const insertMentorProfileSchema = createInsertSchema(mentorProfiles).omit({
  id: true,
  createdAt: true,
});

export const insertSponsorProfileSchema = createInsertSchema(sponsorProfiles).omit({
  id: true,
  createdAt: true,
});

// Types
export type UpsertUser = typeof users.$inferInsert;
export type User = typeof users.$inferSelect;
export type InsertEso = z.infer<typeof insertEsoSchema>;
export type Eso = typeof esos.$inferSelect;
export type InsertStartupIdea = z.infer<typeof insertStartupIdeaSchema>;
export type StartupIdea = typeof startupIdeas.$inferSelect;
export type InsertTeam = z.infer<typeof insertTeamSchema>;
export type Team = typeof teams.$inferSelect;
export type InsertMilestoneProgress = z.infer<typeof insertMilestoneProgressSchema>;
export type UserMilestoneProgress = typeof userMilestoneProgress.$inferSelect;
export type Milestone = typeof milestones.$inferSelect;
export type TeamMember = typeof teamMembers.$inferSelect;
export type MentorProfile = typeof mentorProfiles.$inferSelect;
export type SponsorProfile = typeof sponsorProfiles.$inferSelect;
